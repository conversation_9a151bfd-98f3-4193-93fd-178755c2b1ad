using Godot;
using System;
using System.Collections.Generic;

public partial class animal_basic : Area2D
{
	public bool can_outline = true;
	//回合时间
	public int turn_time = 0;

	public AttributeManagerClass attributeManager = new AttributeManagerClass();
	
	ShaderMaterial shaderMaterial;

	// Called when the node enters the scene tree for the first time.
	public override void _Ready()
	{
		var sprite = GetNode<CanvasGroup>("CanvasGroup");
		shaderMaterial = sprite.Material as ShaderMaterial;
	}

	//设置轮廓大小
	public void outline_set(int width)
	{
		if (can_outline)
			shaderMaterial.SetShaderParameter("line_thickness", width);
	}

	#region 回合处理
	public void Trun_Event(int time_turn_cnt)
	{
		int t = time_turn_cnt;
		if (turn_time != t)
		{
			turn_time = t;
			turn_start();
		}
	}
	//回合开始计算
	public void turn_start()
	{
		// 这里可以添加回合开始时的逻辑
		// 比如重置状态、更新属性等
		Trun_Event(turn_time);
	}

	//回合结束计算
	public void turn_finish()
	{
	}
	#endregion

	public void add_buff(Buff_basic buff)
	{

	}
}
