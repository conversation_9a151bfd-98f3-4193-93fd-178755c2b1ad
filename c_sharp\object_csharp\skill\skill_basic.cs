using Godot;
using GameEnum;
using System;
using System.Collections.Generic;

// 技能目标类型枚举，定义了技能可以作用的目标类型
public enum SkillTargetType
{
    Self, // 自身目标
    SingleEnemy, // 单个敌方目标
    Area, // 区域目标
    
}

//技能范围
//技能具体效果
//技能消耗、cd

/// <summary>
/// 技能基类，提供了技能的基本属性和方法，其他具体技能类需继承该类。
/// </summary>
public abstract class SkillBasic
{
    // 技能的名称，用于标识不同的技能
    public string SkillName { get; set; }
    //技能描述
    public string SkillDescription { get; set; }
    // 技能的类型，对应 SkillType 枚举
    public SkillType SkillType { get; set; }
    
    public SkillTargetType TargetType { get; set; }
    // 技能的作用范围，通常以格子数衡量
    public int SkillRange { get; set; }
    // 技能范围显示对象，用于在游戏中可视化技能的作用范围
    protected SkillRangeDisplay RangeDisplay { get; set; }
    // 技能的冷却时间(原始)，单位为秒
    public float Cooldown { get; set; }
    // 技能的剩余冷却时间
    public float RemainingCooldown { get; set; }
    // 技能的消耗，通常为能量或法力值
    public float Cost { get; set; }

    public SkillBasic()
    {

    }

}